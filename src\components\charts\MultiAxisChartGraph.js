import React, { useState, useMemo, useEffect } from "react";
import {
  Composed<PERSON>hart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { BASE_COLORS_PIE, NO_DATA_MESSAGE } from "../../common/constants";

// Base palette for colors - using the same as BarChartGraph

// Generate colors similar to BarChartGraph
const generateColors = (count) => {
  return Array.from({ length: count }, (_, i) => {
    if (i < BASE_COLORS_PIE.length) return BASE_COLORS_PIE[i];
    const hue = (i * 137.5) % 360;
    return `hsl(${hue}, 70%, 50%)`;
  });
};

// Format numbers with k (thousands) and L (lakhs) notation
const formatNumber = (value) => {
  if (value === 0) return "0";

  const absValue = Math.abs(value);
  const sign = value < 0 ? "-" : "";

  if (absValue >= 100000) {
    // Format in lakhs (L)
    const lakhs = absValue / 100000;
    if (lakhs >= 100) {
      return `${sign}${(lakhs / 100).toFixed(1)}Cr`; // Crores for very large numbers
    }
    return `${sign}${lakhs.toFixed(lakhs >= 10 ? 0 : 1)}L`;
  } else if (absValue >= 1000) {
    // Format in thousands (k)
    const thousands = absValue / 1000;
    return `${sign}${thousands.toFixed(thousands >= 10 ? 0 : 1)}k`;
  }

  return value.toString();
};

function MultiAxisChartGraph({ data = [], config }) {
  const { xAxis, yAxis } = config;
  const orderedData = useMemo(() => [...data], [data]);

  // Keep track of hidden series
  const [hiddenItems, setHiddenItems] = useState(new Set());
  const [showFullLegend, setShowFullLegend] = useState(false);

  // Generate colors for each series
  const colors = useMemo(() => generateColors(yAxis.length), [yAxis.length]);

  // Reset hidden items when data or config changes
  useEffect(() => {
    setHiddenItems(new Set());
  }, [data.length, xAxis, yAxis]);

  // Separate line and bar data based on yAxis configuration
  const lineFields = yAxis
    .filter((item) => item.type === "Line Graph")
    .map((item) => item.name);
  const barFields = yAxis
    .filter((item) => item.type === "Bar Graph")
    .map((item) => item.name);

  // Get all field names for legend
  const allFields = [...lineFields, ...barFields];

  const toggleItem = (fieldName) => {
    setHiddenItems((prev) => {
      const newSet = new Set(prev);
      const visibleCount = allFields.length - prev.size;

      if (newSet.has(fieldName)) {
        newSet.delete(fieldName);
      } else if (visibleCount > 1) {
        newSet.add(fieldName);
      }
      return newSet;
    });
  };

  // Filter out hidden fields
  const visibleLineFields = lineFields.filter(
    (field) => !hiddenItems.has(field)
  );
  const visibleBarFields = barFields.filter((field) => !hiddenItems.has(field));

  // Create Y-axis configurations similar to BarChartGraph
  const visibleYAxes = allFields
    .map((field, index) => ({
      field,
      originalIndex: index,
      color: colors[index],
      yAxisId: `yAxis-${index}`,
      type: yAxis.find((item) => item.name === field)?.type || "Bar Graph",
    }))
    .filter((axis) => !hiddenItems.has(axis.field))
    .map((axis, visibleIndex) => {
      const orientation = visibleIndex % 2 === 0 ? "left" : "right";
      const offset = Math.floor(visibleIndex / 2) * 80;

      return {
        ...axis,
        orientation,
        offset,
      };
    });

  // Mirror LineChartGraph behavior: if the incoming data array is empty, show a simple message
  if (!data?.length) {
    return (
      <div className="w-full h-60 flex justify-center items-center text-gray-500 font-medium">
        {NO_DATA_MESSAGE}
      </div>
    );
  }

  // Enhanced custom tooltip similar to BarChartGraph
  const CustomTooltip = ({
    active,
    payload,
    label,
    hiddenItems = new Set(),
  }) => {
    if (!active || !payload || !payload.length) return null;

    // Filter out hidden items from tooltip
    const visiblePayload = payload.filter(
      (entry) => !hiddenItems.has(entry.dataKey)
    );

    if (visiblePayload.length === 0) return null;

    return (
      <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg text-xs">
        <p className="mb-2 font-bold text-sm text-gray-800">{`${xAxis}: ${label}`}</p>
        <div className="space-y-2">
          {visiblePayload.map((entry, index) => (
            <div key={index} className="flex justify-between items-center">
              <div className="flex items-center">
                <span className="font-medium" style={{ color: entry.color }}>
                  {entry.name}
                </span>
              </div>
              <span className="text-gray-900 font-bold ml-3">
                {entry.value?.toLocaleString() ?? 0}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Enhanced custom legend similar to BarChartGraph
  const CustomLegend = ({ payload = [], hiddenItems, onToggleItem }) => {
    const visibleItems = showFullLegend ? payload : payload.slice(0, 12);
    const visibleItemCount = payload.length - hiddenItems.size;

    return (
      <div className="bg-white border border-gray-400 rounded-lg p-3 m-5">
        <div className="flex flex-wrap gap-6 items-center justify-start">
          {visibleItems.map((entry, index) => {
            const isHidden = hiddenItems.has(entry.id);
            const canHide = visibleItemCount > 1;
            const canClick = isHidden || canHide;

            return (
              <div
                key={index}
                onClick={() => canClick && onToggleItem(entry.id)}
                className={`
                  flex items-center gap-2 transition-all duration-200
                  ${
                    canClick
                      ? "cursor-pointer"
                      : "cursor-not-allowed opacity-70"
                  }
                  ${canClick ? "hover:opacity-80" : ""}
                `}
              >
                <span
                  className={`
                    w-3 h-3 rounded-full flex-shrink-0
                    ${isHidden ? "opacity-30" : ""}
                  `}
                  style={{
                    backgroundColor: entry.color,
                  }}
                />
                <span
                  className="text-xs font-medium"
                  style={{
                    color: entry.color,
                    textDecoration: isHidden ? "line-through" : "none",
                  }}
                >
                  {entry.value}
                </span>
              </div>
            );
          })}
        </div>
        {payload.length > 12 && (
          <div className="mt-3 text-center">
            <button
              onClick={() => setShowFullLegend((prev) => !prev)}
              className="text-blue-600 hover:text-blue-800 transition-colors text-xs font-medium"
            >
              {showFullLegend ? "Show Less ▲" : "Show All ▼"}
            </button>
          </div>
        )}
      </div>
    );
  };

  // Legend payload with field information
  const legendPayload = allFields.map((field, index) => ({
    color: colors[index],
    value: field,
    id: field,
    yAxisId: `yAxis-${index}`,
  }));

  return (
    <>
      {" "}
      <div className="w-full h-96 overflow-x-auto">
        <div
          style={{
            width: Math.max(orderedData.length * 50, 1000),
            height: "100%",
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart
              data={data}
              margin={{ top: 20, right: 30, left: 0, bottom: 0 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey={xAxis}
                angle={-45}
                textAnchor="end"
                height={100}
                interval={0}
                fontSize={11}
                fontWeight={600}
                tickFormatter={(value) =>
                  value?.length > 20 ? value.substring(0, 20) + "..." : value
                }
              />

              {/* Render multiple Y-axes with dynamic positioning based on visible axes */}
              {visibleYAxes.map((yAxisConfig) => (
                <YAxis
                  key={yAxisConfig.yAxisId}
                  yAxisId={yAxisConfig.yAxisId}
                  orientation={yAxisConfig.orientation}
                  offset={yAxisConfig.offset}
                  width={60} // Fixed width for consistent spacing
                  tick={{
                    fontSize: 10,
                    fill: yAxisConfig.color,
                    fontWeight: "bold",
                  }}
                  axisLine={{
                    stroke: yAxisConfig.color,
                    strokeWidth: 2,
                    opacity: 0.8,
                  }}
                  tickLine={{
                    stroke: yAxisConfig.color,
                    strokeWidth: 1,
                    opacity: 0.6,
                  }}
                  tickFormatter={formatNumber}
                  label={{
                    value: yAxisConfig.field,
                    angle: -90, // always vertical
                    position:
                      yAxisConfig.orientation === "left"
                        ? "insideLeft"
                        : "insideRight",
                    offset: 20,
                    style: {
                      textAnchor: "middle",
                      fill: yAxisConfig.color,
                      fontSize: "11px",
                      fontWeight: "bold",
                    },
                  }}
                />
              ))}

              <Tooltip
                content={<CustomTooltip hiddenItems={hiddenItems} />}
                wrapperStyle={{ zIndex: 1000, outline: "none" }}
              />

              {/* Render Bar components */}
              {visibleBarFields.map((field) => {
                const originalIndex = allFields.indexOf(field);
                const yAxisId = `yAxis-${originalIndex}`;
                const barSize = Math.min(
                  40, // Maximum bar width
                  Math.max(
                    15, // Minimum bar width
                    Math.floor(
                      400 / (visibleBarFields.length * orderedData.length)
                    ) + 20
                  )
                );
                return (
                  <Bar
                    key={`bar-${field}`}
                    yAxisId={yAxisId}
                    dataKey={field}
                    fill={colors[originalIndex]}
                    name={field}
                    barSize={barSize}
                  />
                );
              })}

              {/* Render Line components */}
              {visibleLineFields.map((field) => {
                const originalIndex = allFields.indexOf(field);
                const yAxisId = `yAxis-${originalIndex}`;
                return (
                  <Line
                    key={`line-${field}`}
                    yAxisId={yAxisId}
                    type="monotone"
                    dataKey={field}
                    stroke={colors[originalIndex]}
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    name={field}
                  />
                );
              })}
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>
      {/* Enhanced Legend */}
      <CustomLegend
        payload={legendPayload}
        hiddenItems={hiddenItems}
        onToggleItem={toggleItem}
      />
    </>
  );
}

export default MultiAxisChartGraph;
