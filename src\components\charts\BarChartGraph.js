import React, { useMemo, useState } from "react";
import {
  Composed<PERSON><PERSON>,
  Bar,
  XAxis,
  <PERSON>Axis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import { BASE_COLORS_PIE, NO_DATA_MESSAGE } from "../../common/constants";

// Custom Tooltip Component
const CustomTooltip = ({ active, payload, label, hiddenBars = new Set() }) => {
  if (!active || !payload || !payload.length) return null;

  // Filter out hidden bars from tooltip
  const visiblePayload = payload.filter(
    (entry) => !hiddenBars.has(entry.dataKey)
  );

  if (visiblePayload.length === 0) return null;

  return (
    <div className="bg-white p-3 border border-gray-300 rounded-lg shadow-lg text-xs">
      <p className="mb-2 font-bold text-sm text-gray-800">{`${label}`}</p>
      <div className="space-y-2">
        {visiblePayload.map((entry, index) => (
          <div key={index} className="flex justify-between items-center">
            <div className="flex items-center">
              <span className="font-medium " style={{ color: entry.color }}>
                {entry.name}
              </span>
            </div>
            <span className="text-gray-900 font-bold ml-3">
              {entry?.value?.toLocaleString()}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

// Custom Legend Component
const CustomLegend = ({ payload = [], hiddenBars, onToggleBar }) => {
  const [showFullLegend, setShowFullLegend] = useState(false);

  const visibleItems = showFullLegend ? payload : payload.slice(0, 12);
  const visibleBarCount = payload.length - hiddenBars.size;

  return (
    <div className="bg-white border border-gray-400 rounded-lg p-3 m-5">
      <div className="flex flex-wrap gap-6 items-center justify-start">
        {visibleItems.map((entry, index) => {
          const isHidden = hiddenBars.has(entry.id);
          const canHide = visibleBarCount > 1;
          const canClick = isHidden || canHide;

          return (
            <div
              key={index}
              onClick={() => canClick && onToggleBar(entry.id)}
              className={`
                flex items-center gap-2 transition-all duration-200
                ${canClick ? "cursor-pointer" : "cursor-not-allowed opacity-70"}
                ${canClick ? "hover:opacity-80" : ""}
              `}
            >
              <span
                className={`
                  w-3 h-3 rounded-full flex-shrink-0
                  ${isHidden ? "opacity-30" : ""}
                `}
                style={{
                  backgroundColor: entry.color,
                }}
              />
              <span
                className={`
                  text-xs font-medium
                `}
                style={{
                  color: entry.color,
                  textDecoration: isHidden ? "line-through" : "none",
                }}
              >
                {entry.value}
              </span>
            </div>
          );
        })}
      </div>
      {payload.length > 12 && (
        <div className="mt-3 text-center">
          <button
            onClick={() => setShowFullLegend((prev) => !prev)}
            className="text-blue-600 hover:text-blue-800 transition-colors text-xs font-medium"
          >
            {showFullLegend ? "Show Less ▲" : "Show All ▼"}
          </button>
        </div>
      )}
    </div>
  );
};

function BarChartMultipleYAxes({ data = [], config = {} }) {
  const [hiddenBars, setHiddenBars] = useState(new Set());

  const handleToggleBar = (barId) => {
    setHiddenBars((prev) => {
      const newSet = new Set(prev);
      const currentVisibleCount = yAxisMetrics.length - prev.size;

      if (newSet.has(barId)) {
        newSet.delete(barId);
      } else {
        if (currentVisibleCount > 1) {
          newSet.add(barId);
        }
      }
      return newSet;
    });
  };

  // Reset hidden bars when data or config changes
  React.useEffect(() => {
    setHiddenBars(new Set());
  }, [data.length, config?.xAxis, config?.yAxis]);

  // 🎨 Colors
  const generateColors = (count) => {
    return Array.from({ length: count }, (_, i) => {
      if (i < BASE_COLORS_PIE.length) return BASE_COLORS_PIE[i];
      const hue = (i * 137.5) % 360;
      return `hsl(${hue}, 70%, 50%)`;
    });
  };

  // 🔍 Metrics
  const yAxisMetrics = useMemo(() => {
    return (
      config.yAxis?.filter((metric) => metric !== "Select All") || ["Count"]
    );
  }, [config.yAxis]);

  const colors = useMemo(
    () => generateColors(yAxisMetrics.length),
    [yAxisMetrics.length]
  );

  // 📊 Keep order same as API response
  const orderedData = useMemo(() => {
    // Process data to handle empty strings in xAxis
    return data.map((item) => {
      const processedItem = { ...item };

      // Handle empty string in xAxis
      if (processedItem[config.xAxis] === "") {
        processedItem[config.xAxis] = "Empty String";
      }

      // Handle empty strings in yAxis fields
      yAxisMetrics.forEach((metric) => {
        if (processedItem[metric] === "") {
          processedItem[metric] = "Empty String";
        }
      });

      return processedItem;
    });
  }, [data, config.xAxis, yAxisMetrics]);

  const visibleMetrics = yAxisMetrics.filter(
    (metric) => !hiddenBars.has(metric)
  );

  if (!data?.length) {
    return (
      <div className="w-full h-60 flex justify-center items-center text-gray-500 font-medium">
        {NO_DATA_MESSAGE}
      </div>
    );
  }

  // Create Y-axis configurations - only for visible metrics with dynamic positioning
  const visibleYAxes = yAxisMetrics
    .map((metric, index) => ({
      metric,
      originalIndex: index,
      color: colors[index],
      yAxisId: `yAxis-${index}`,
    }))
    .filter((axis) => !hiddenBars.has(axis.metric))
    .map((axis, visibleIndex) => {
      // Recalculate positioning based on visible axes only
      const orientation = visibleIndex % 2 === 0 ? "left" : "right";
      const offset = Math.floor(visibleIndex / 2) * 80;

      return {
        ...axis,
        orientation,
        offset,
      };
    });

  // Legend payload with Y-axis information
  const legendPayload = yAxisMetrics.map((metric, index) => ({
    color: colors[index],
    value: metric,
    id: metric,
    yAxisId: `yAxis-${index}`,
  }));

  return (
    <>
      <div className="w-full h-96 overflow-x-auto">
        <div
          style={{
            width: Math.max(orderedData.length * 50, 1000),
            height: "100%",
          }}
        >
          <ResponsiveContainer width="100%" height="100%">
            <ComposedChart
              data={orderedData}
              margin={{ top: 20, right: 30, left: 0, bottom: 10 }}
            >
              <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
              <XAxis
                dataKey={config.xAxis}
                angle={-45}
                textAnchor="end"
                height={100}
                interval={0}
                fontSize={11}
                fontWeight={600}
                tickFormatter={(value) =>
                  value?.length > 20 ? value.substring(0, 20) + "..." : value
                }
              />

              {/* Render multiple Y-axes with dynamic positioning based on visible axes */}
              {visibleYAxes.map((yAxisConfig) => (
                <YAxis
                  key={yAxisConfig.yAxisId}
                  yAxisId={yAxisConfig.yAxisId}
                  orientation={yAxisConfig.orientation}
                  offset={yAxisConfig.offset}
                  width={60} // Fixed width for consistent spacing
                  tick={{
                    fontSize: 10,
                    fill: yAxisConfig.color,
                    fontWeight: "bold",
                  }}
                  axisLine={{
                    stroke: yAxisConfig.color,
                    strokeWidth: 2,
                    opacity: 0.8,
                  }}
                  tickLine={{
                    stroke: yAxisConfig.color,
                    strokeWidth: 1,
                    opacity: 0.6,
                  }}
                  tickFormatter={(value) => {
                    if (value >= 100000) {
                      return `${(value / 100000).toFixed(1)}L`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(1)}K`;
                    }
                    return value?.toString() || "0";
                  }}
                  label={{
                    value: yAxisConfig.metric,
                    angle: -90, // always vertical
                    position:
                      yAxisConfig.orientation === "left"
                        ? "insideLeft"
                        : "insideRight",
                    offset: 20, // no extra push
                    style: {
                      textAnchor: "middle",
                      fill: yAxisConfig.color,
                      fontSize: "11px",
                      fontWeight: "bold",
                    },
                  }}
                />
              ))}

              <Tooltip
                content={<CustomTooltip hiddenBars={hiddenBars} />}
                wrapperStyle={{ zIndex: 1000, outline: "none" }}
              />

              {/* Render Bars with improved spacing and alignment */}
              {visibleMetrics.map((metric, index) => {
                const originalIndex = yAxisMetrics.indexOf(metric);
                const yAxisId = `yAxis-${originalIndex}`;
                const barSize = Math.min(
                  40, // Maximum bar width
                  Math.max(
                    15, // Minimum bar width
                    Math.floor(
                      400 / (visibleMetrics.length * orderedData.length)
                    ) + 20
                  )
                );

                return (
                  <Bar
                    key={metric}
                    dataKey={metric}
                    fill={colors[originalIndex]}
                    name={metric}
                    barSize={barSize}
                    yAxisId={yAxisId}
                  />
                );
              })}
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      </div>

      <CustomLegend
        payload={legendPayload}
        hiddenBars={hiddenBars}
        onToggleBar={handleToggleBar}
      />
    </>
  );
}

export default BarChartMultipleYAxes;
