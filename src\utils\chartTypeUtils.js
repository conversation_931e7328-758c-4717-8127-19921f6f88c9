import React from "react";
import {
  BarButton,
  GaugeChartButton,
  HeatMapButton,
  LineButtonIcon,
  MultiAxisButton,
  PieChartButton,
  ScatterPlotButton,
  LineSelectIcon,
  BarSelectIcon,
  MultiAxisSelectIcon,
  PieChartSelectIcon,
  ScatterPlotSelectIcon,
  GaugeChartSelectIcon,
  HeatMapSelectIcon,
} from "../icons";
import CustomDropDown from "../components/Dropdown/ReportsDropdownList";
import InputLabel from "../components/FormsUI/InputLabel/InputLabel";

export const CHART_TYPES = [
  {
    name: "Line",
    value: "line",
    Icon: LineButtonIcon,
    SelectIcon: LineSelectIcon,
  },
  {
    name: "Bar",
    value: "bar",
    Icon: BarButton,
    SelectIcon: BarSelectIcon,
  },
  {
    name: "Heat",
    value: "heat",
    Icon: HeatMapButton,
    SelectIcon: HeatMapSelectIcon,
  },
  {
    name: "Pie",
    value: "pie",
    Icon: Pie<PERSON>hartButton,
    SelectIcon: PieChartSelectIcon,
  },
  {
    name: "Multi Graph",
    value: "multiAxis",
    Icon: MultiAxisButton,
    SelectIcon: MultiAxisSelectIcon,
  },
  {
    name: "Scatter Plot",
    value: "scatter",
    Icon: ScatterPlotButton,
    SelectIcon: ScatterPlotSelectIcon,
  },
  {
    name: "Gauge Chart",
    value: "gauge",
    Icon: GaugeChartButton,
    SelectIcon: GaugeChartSelectIcon,
  },
];

// Memoized filter field component to reduce re-renders
export const FilterField = React.memo(
  ({
    filterName,
    shouldShow,
    label,
    isMandatory = false,
    data,
    btnName,
    values,
    setFieldValue,
    filterData,
    activeView,
    panelVisualizationType,
    configApiData,
    errors,
    touched,
  }) => {
    if (!shouldShow) return null;

    const optionDataList =
      activeView === "graph" && panelVisualizationType === "Table Report"
        ? configApiData?.GRAPH_FILTER_DD_LIMIT
        : activeView !== "table"
        ? configApiData?.GRAPH_FILTER_DD_LIMIT
        : "";

    return (
      <div>
        <InputLabel label={label} isMandatory={isMandatory} />
        <CustomDropDown
          btnWidth="w-full"
          data={data || []}
          btnName={btnName}
          onSelectionChange={(selectedDetail) => {
            setFieldValue(filterName, selectedDetail);
          }}
          value={values[filterName]}
          defaultSelectedData={filterData?.[filterName] || []}
          optionDataList={optionDataList}
        />
        {errors[filterName] && touched[filterName] && (
          <div className="text-red-500 text-xs">{errors[filterName]}</div>
        )}
      </div>
    );
  }
);

FilterField.displayName = "FilterField";
