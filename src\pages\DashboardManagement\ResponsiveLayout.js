import React, { forwardRef, useContext, useEffect, useState } from "react";
import { WidthProvider, Responsive } from "react-grid-layout";
import PanelVisualization from "./PanelVisualization";
import {
  CloseIcon,
  DownloadIcon,
  ExpandIcon,
  ExportSaveIcon,
  InfoIcon,
} from "../../icons";
import { CircularProgress } from "@mui/material";
import ExportPopup from "../../popups/exportpopup";
import ExpandDashboard from "../../popups/ExpandDashboard";
import { formatDate } from "../../utils/fileDateFormator";
import { downloadPanel, previewPanel } from "../../services/panels-api";
import { DownloadContext } from "../../context/DownloadContext";
import { CssTooltip } from "../../components/StyledComponent";
import { ConditionDisplay } from "../../common/constants";
import SuccessDialog from "../../popups/SuccessDialog";
import { AuthContext } from "../../context/AuthContext";
import ErrorDialog from "../../popups/ErrorDialog";

const ResponsiveGridLayout = WidthProvider(Responsive);

const ResponsiveLayout = forwardRef(
  (
    {
      previewLoading,
      setPreviewLoading,
      removePanel,
      panelDroppedData,
      getGridData,
      isCreate,
      isEdit,
      isDownload,
      errorCode,
      reportFilters,
      selectedFilter,
      isApplied,
      userAnalysis,
    },
    ref
  ) => {
    const colors = ["#EDDF82", "#82C3ED", "#82EDAD", "#ED8282"];
    const [showExportConfirmation, setShowExportConfirmation] = useState(false);
    const [expandDialog, setExpandDialog] = useState(false);
    const [exportPanelData, setExportPanelData] = useState("");
    const [showSuccessDialog, setShowSuccessDialog] = useState(false);
    const [errorDialog, setErrorDialog] = useState(false);
    const [permission, setPermission] = useState({
      csv: false,
      pdf: false,
      excel: false,
    });
    const [message, setMessage] = useState("");
    const { user, configApiData } = useContext(AuthContext);
    const [totalCount, setTotalCount] = useState("");
    const { setIsDownloading, setIsSaving } = useContext(DownloadContext);
    useEffect(() => {
      const isLoading = panelDroppedData.some(
        (panelObj) => !panelObj?.panelData || panelObj.id === "dummy"
      );
      setPreviewLoading(isLoading);
    }, [panelDroppedData, setPreviewLoading]);

    const isExportAllowed = (totalCount) => {
      const {
        MAX_ROWS_FOR_CSV,
        MAX_ROWS_FOR_PDF,
        MAX_ROWS_FOR_EXCEL,
        INITIATE_OFFLINE_DOWNLOAD,
      } = configApiData;

      const isOfflineDownload = totalCount >= INITIATE_OFFLINE_DOWNLOAD;

      return {
        csv: isOfflineDownload || totalCount <= MAX_ROWS_FOR_CSV,
        pdf: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_PDF,
        excel: isOfflineDownload ? false : totalCount <= MAX_ROWS_FOR_EXCEL,
      };
    };

    const tooltipContent = (
      <div className="text-sm">
        <div className="font-bold text-xs mb-1">
          Configured number of rows for download:
        </div>
        <div className="font-semibold text-xs mb-1">
          {" "}
          CSV:{" "}
          {configApiData?.MAX_ROWS_FOR_CSV?.toLocaleString("en-US") ||
            "Loading..."}
        </div>
        <div className="font-semibold text-xs mb-1">
          EXCEL:{" "}
          {configApiData?.MAX_ROWS_FOR_EXCEL?.toLocaleString("en-US") ||
            "Loading..."}
        </div>
        <div className="font-semibold text-xs mb-1">
          PDF:{" "}
          {configApiData?.MAX_ROWS_FOR_PDF?.toLocaleString("en-US") ||
            "Loading..."}
        </div>
        <div className="font-semibold text-xs mb-1">
          Offline (Only in CSV):{" "}
          {configApiData?.INITIATE_OFFLINE_DOWNLOAD?.toLocaleString("en-US") ||
            "Loading..."}
        </div>
      </div>
    );

    const exportReport = (type) => {
      if (type !== "") {
        let extension =
          type === "PDF" ? ".pdf" : type === "EXCEL" ? ".xlsx" : ".csv";
        if (totalCount < configApiData.INITIATE_OFFLINE_DOWNLOAD) {
          setIsDownloading(true);
        }
        if (exportPanelData) {
          const data = exportPanelData?.panelData;
          const startDate = data.startDate.split(" ")[0];
          const endDate = data.endDate.trim().split(" ")[0];

          const currentTime = formatDate();
          let reqData = {
            download: true,
            type: type,
            name: data.name,
            visualizationType: data.visualizationType,
            filters: [],
            dataColumns: {
              derivedFields: data.dataColumns
                ? data.dataColumns.derivedFields
                : [],
            },
            reportFilters: reportFilters,
            ...(data.interval && !isApplied ? { interval: data.interval } : {}),
            startDate: isApplied ? selectedFilter.startDate : data.startDate,
            endDate: isApplied ? selectedFilter.endDate : data.endDate,
          //  fileName: `${data.name} ${startDate} - ${endDate} ${currentTime}`,
          };
          if (data.visualizationType === "Table Report") {
            reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
          }
          if (data.visualizationType === "Bar Graph") {
            reqData.dataColumns["X-Axis"] = data?.dataColumns?.["X-Axis"];
            reqData.dataColumns.noOfRecords = data?.dataColumns?.noOfRecords;
          }

          data.filters.forEach((condition) => {
            reqData.filters.push({
              field: condition.field,
              condition: condition.condition,
              value: condition.value,
              operator: condition.operator,
            });
          });
          return downloadPanel({ reqData })
            .then((blob) => {
              if (totalCount > configApiData.INITIATE_OFFLINE_DOWNLOAD) {
                setShowSuccessDialog(true);
                setMessage(
                  "Your offline download is initiated, Please view the status of your download in Offline Downloads menu"
                );
              } else {
                const url = URL.createObjectURL(blob.data);
                const link = document.createElement("a");
                const sanitize = (str) =>
                  str.replace(/\s+/g, "_").replace(/[^a-zA-Z0-9_\-]/g, "");

                link.href = url;
                const startDate = data.startDate.split(" ")[0];
                const endDate = data.endDate.trim().split(" ")[0];
                const currentTime = formatDate();
                const filename = `${sanitize(
                  data.name
                )}_${startDate}_${endDate}_${currentTime}${extension}`;

                link.download = filename + ".zip";
                link.click();
              }
            })
            .catch(() => {
              setIsDownloading(false);
            })
            .finally(() => {
              setIsDownloading(false);
            });
        }
      }
    };

    const handleDownloadIconClick = (index, totalCount) => {
      setShowExportConfirmation(true);
      const panelData = panelDroppedData[index];
      setExportPanelData(panelData);
    };
    const handleExpandIconClick = (data) => {
      setExpandDialog(true);
      const panelData = data;
      setExportPanelData(panelData);
    };
    const handleSaveIconClick = (inputData) => {
      if (inputData) {
        const data = inputData?.panelData;
        setIsSaving(true);
        let reqData = {
          save: 1,
          name: data.name,
          visualizationType: data.visualizationType,
          filters: [],
          dataColumns: {
            derivedFields: data.dataColumns
              ? data.dataColumns.derivedFields
              : [],
          },
        };
        if (data.visualizationType === "Table Report") {
          reqData.dataColumns.tableFields = data?.dataColumns?.tableFields;
          reqData.startDate =
            inputData?.startDate !== undefined
              ? inputData?.startDate
              : data?.startDate;
          reqData.endDate =
            inputData?.endDate !== undefined
              ? inputData?.endDate
              : data?.endDate;
        }

        data.filters.forEach((condition) => {
          reqData.filters.push({
            field: condition.field,
            condition: condition.condition,
            value: condition.value,
            operator: condition.operator,
          });
        });
        return previewPanel({ reqData })
          .then((resp) => {
            if (resp?.data) {
              setIsSaving(false);
              setShowSuccessDialog(true);
              setMessage(resp?.data);
            }
          })
          .catch((error) => {
            setIsSaving(false);
            setErrorDialog(true);

            setMessage(
              error?.message ? error?.message : "Unable to save the file"
            );
          })
          .finally(() => {
            setIsSaving(false);
          });
      }
    };

    return (
      <>
        <div className=" " ref={ref}>
          <ResponsiveGridLayout
            className="layout"
            cols={{ lg: 6, md: 6, sm: 6, xs: 6, xxs: 6 }}
            rowHeight={110}
            isDraggable={false}
            isResizable={false}
          >
            {Array.isArray(panelDroppedData) &&
              panelDroppedData.map((panelObj, index) => (
                <div
                  key={index + "panel"}
                  data-grid={getGridData(index, panelObj?.dimension)}
                >
                  <div className="bg-white p-3">
                    <div className="flex items-center">
                      <div className="text-xs text-titleColor ml-2 whitespace-pre-wrap break-all flex-grow">
                        {panelObj?.panelData?.name}
                      </div>
                      <div className="flex gap-2">
                        {panelObj?.panelData?.visualizationType ===
                          "Table Report" &&
                        user.isSuperAdmin &&
                        !userAnalysis &&
                        panelObj?.panelData?.data !== "failed" ? (
                          <CssTooltip
                            title={"Save in configured path"}
                            placement="top"
                            arrow
                          >
                            <ExportSaveIcon
                              className="w-3.5 h-3.5 cursor-pointer mr-1"
                              onClick={(event) => {
                                event.stopPropagation();
                                event.preventDefault();
                                if (panelObj?.panelData) {
                                  handleSaveIconClick(panelObj);
                                }
                              }}
                            />
                          </CssTooltip>
                        ) : null}
                        {panelObj?.panelData?.data !== "failed" ? (
                          <CssTooltip title={"Expand"} placement="top" arrow>
                            <ExpandIcon
                              className="w-3 h-3 cursor-pointer"
                              onClick={(event) => {
                                event.stopPropagation();
                                event.preventDefault();
                                if (panelObj?.panelData) {
                                  handleExpandIconClick(panelObj);
                                }
                              }}
                            />
                          </CssTooltip>
                        ) : null}

                        {panelObj?.panelData &&
                          (isCreate || isEdit) &&
                          !previewLoading && (
                            <CssTooltip title={"Close"} placement="top" arrow>
                              <CloseIcon
                                className="w-2.5 h-2.5 cursor-pointer ml-2"
                                onClick={(event) => {
                                  event.stopPropagation();
                                  event.preventDefault();
                                  removePanel(index);
                                }}
                              />
                            </CssTooltip>
                          )}

                        {isDownload &&
                          panelObj?.panelData?.data !== "failed" &&
                          panelObj?.count !== 0 && (
                            <CssTooltip
                              title={tooltipContent}
                              placement="top"
                              arrow
                              PopperProps={{
                                modifiers: [
                                  {
                                    name: "preventOverflow",
                                    options: {
                                      altBoundary: true,
                                    },
                                  },
                                ],
                              }}
                              enterNextDelay={100}
                              enterDelay={100}
                              leaveDelay={200}
                              componentsProps={{
                                popper: {
                                  sx: {
                                    opacity: 1,
                                  },
                                },
                              }}
                            >
                              <DownloadIcon
                                className="w-3.5 h-3.5 cursor-pointer ml-2"
                                onClick={(event) => {
                                  event.stopPropagation();
                                  event.preventDefault();
                                  if (panelObj?.panelData) {
                                    const permission = isExportAllowed(
                                      panelObj?.count
                                    );
                                    setTotalCount(panelObj?.count);
                                    setPermission(permission);
                                    handleDownloadIconClick(index);
                                  }
                                }}
                              />
                            </CssTooltip>
                          )}
                      </div>
                    </div>

                    <div className="border-b border-panelBorder mt-2 mb-3"></div>

                    {!panelObj?.panelData || panelObj.id === "dummy" ? (
                      <div className="my-3 flex justify-center items-center">
                        <CircularProgress
                          size={20}
                          style={{ color: "black" }}
                        />
                      </div>
                    ) : (
                      <>
                        <div className="text-headingColor text-xs font-medium mb-2">
                          {"From: " +
                            (isApplied
                              ? selectedFilter?.startDate
                              : panelObj?.panelData?.startDate ||
                                panelObj?.startDate ||
                                "")}
                          {" - "}
                          {"To: " +
                            (isApplied
                              ? selectedFilter?.endDate
                              : panelObj?.panelData?.endDate ||
                                panelObj?.endDate ||
                                "")}
                        </div>
                        {!isApplied ? (
                          <div className="text-headingColor text-xs font-medium flex mb-2">
                            Selected Filters:
                            <CssTooltip
                              title={
                                <ConditionDisplay
                                  conditions={panelObj?.panelData?.filters}
                                />
                              }
                              placement="left"
                              arrow
                            >
                              <InfoIcon className="ml-2 mr-4 w-4 h-3.5" />
                            </CssTooltip>
                          </div>
                        ) : null}

                        {panelObj?.panelData?.data === "failed" ? (
                          <div className="my-3 text-xs flex justify-center items-center p-10">
                            {errorCode
                              ? errorCode
                              : panelObj?.errorMessage
                              ? panelObj?.errorMessage
                              : "Something went wrong!!"}
                          </div>
                        ) : (
                          <PanelVisualization
                            type={panelObj?.panelData?.visualizationType}
                            data={panelObj?.panelData?.data}
                            dimension={panelObj?.dimension}
                            colors={colors}
                            isChart={true}
                          />
                        )}
                      </>
                    )}
                  </div>
                </div>
              ))}
          </ResponsiveGridLayout>
        </div>
        <ExportPopup
          show={showExportConfirmation}
          onHide={() => setShowExportConfirmation(false)}
          onConfirm={(type) => {
            exportReport(type);
            setShowExportConfirmation(false);
          }}
          title={"Export Report"}
          identity={"Reports"}
          exportPermissions={permission}
        />
        <ExpandDashboard
          open={expandDialog}
          handleClose={() => setExpandDialog(false)}
          panelData={exportPanelData}
          isApplied={isApplied}
        />
        <SuccessDialog
          show={showSuccessDialog}
          onHide={() => {
            setShowSuccessDialog(false);
          }}
          message={message}
          btnName={"Okay"}
        />
        <ErrorDialog
          show={errorDialog}
          onHide={() => setErrorDialog(false)}
          message={message}
        />
      </>
    );
  }
);

export default ResponsiveLayout;
