import Dialog from "@mui/material/Dialog";
import DialogTitle from "@mui/material/DialogTitle";
import { Alert, CloseIcon } from "../../icons";
import Button from "../../components/Button/OutlinedButton";
import ReApplyButton from "../../components/Button/Button";

function InfoModal({
  show,
  onHide,
  message,
  btnName,
  msg,
  graph,
  openGraphFilter,
  applyFilter,
}) {
  return (
    <Dialog
      sx={{
        "& .MuiDialog-paper": {
          width: "100%",
          maxWidth: 411,
          maxHeight: 435,
        },
      }}
      maxWidth="xs"
      open={show}
      onClose={graph ? undefined : onHide}
      className="p-6 font-sans"
    >
      <DialogTitle className="mt-2 flex justify-end">
        {!graph && (
          <CloseIcon className="w-2.5 h-2.5 cursor-pointer" onClick={onHide} />
        )}
      </DialogTitle>
      <div className="border-b border-panelBorder mx-5" />
      <div className="flex justify-center items-center mt-5">
        <Alert className="w-10 h-10" />
      </div>

      <div className="text-xl mt-4 mx-6 text-center font-medium ">{msg}</div>
      <div
        className={` mt-4 mx-6 text-center font-medium ${
          msg ? "text-tabColor text-sm" : "text-black text-base"
        }`}
      >
        {message}
      </div>

      <div>
        <div className="text-center mt-10 mb-5 flex justify-center items-center gap-5">
          {graph ? (
            <ReApplyButton
              onClick={() => {
                openGraphFilter();
                onHide();
              }}
              label={"Re-Apply"}
              buttonClassName="w-[100px] h-9 text-xs"
            />
          ) : null}
          <Button
            onClick={() => {
              if (graph) {
                applyFilter();
              }
              onHide();
            }}
            type="submit"
            label={btnName ? btnName : graph ? "Apply" : "Okay"}
            buttonClassName="w-[100px] h-9 text-xs"
          />
        </div>
      </div>
    </Dialog>
  );
}

export default InfoModal;
