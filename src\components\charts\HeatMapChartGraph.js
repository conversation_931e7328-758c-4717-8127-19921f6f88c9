import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tooltip,
  CircularProgress,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { NO_DATA_MESSAGE } from "../../common/constants";

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  maxHeight: 800,
  overflow: "auto",
  position: "relative", // Ensure proper positioning context
  "& .MuiTableCell-root": {
    border: `1px solid ${theme.palette.divider}`,
    padding: theme.spacing(1.5),
  },
  // Ensure proper stacking context
  "& .MuiTable-root": {
    borderCollapse: "separate",
    borderSpacing: 0,
    position: "relative", // Add positioning context for the table
  },
  // Force hardware acceleration for better sticky performance
  "& .MuiTableCell-stickyHeader": {
    transform: "translateZ(0)",
  },
}));

const StyledHeaderCell = styled(TableCell)(({ theme }) => ({
  backgroundColor: "#E7F1FD",
  fontWeight: 600,
  fontSize: "0.75rem",
  position: "sticky",
  top: 0,
  zIndex: 1000, // Increased z-index
  minWidth: 150,
  transform: "translateZ(0)", // Force hardware acceleration
}));

const StickyCell = styled(TableCell)(({ theme }) => ({
  backgroundColor: "#E7F1FD",
  fontWeight: 500,
  fontSize: "0.75rem",
  position: "sticky",
  left: 0,
  zIndex: 999, // Increased z-index
  minWidth: 150,
  maxWidth: 130,
  width: 150,
  transform: "translateZ(0)", // Force hardware acceleration
}));

// Corner cell (intersection of sticky header and sticky column)
const CornerCell = styled(TableCell)(({ theme }) => ({
  backgroundColor: "#E7F1FD",
  fontWeight: 600,
  fontSize: "0.75rem",
  position: "sticky",
  top: 0,
  left: 0,
  zIndex: 1001, // Highest z-index for corner
  minWidth: 80,
  maxWidth: 100,
  width: 80,
  transform: "translateZ(0)", // Force hardware acceleration
}));

const HeatmapCell = styled(TableCell)(({ backgroundColor }) => ({
  backgroundColor,
  textAlign: "center",
  fontSize: "0.75rem",
  fontWeight: 500,
  minWidth: 100,
  height: 48,
  position: "relative",
  zIndex: 1,
  "&:hover": {
    transform: "scale(1.02)",
    transition: "transform 0.2s ease-in-out",
    zIndex: 50, // Lower than sticky cells
    boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
  },
}));

const ColorRangeBar = styled(Box)(({ gradient }) => ({
  width: 300,
  height: 20,
  background: gradient,
  borderRadius: 4,
  border: "1px solid #e0e0e0",
}));

function HeatMapChartGraph({ data = [], config }) {
  // Default config
  const defaultConfig = {
    xAxis: "Customer",
    yAxis: "Customer Bind",
    derivedFields: "Count",
    mapColor: "#00FF00",
  };

  const chartConfig = { ...defaultConfig, ...config };

  // Lazy loading state
  const [visibleRows, setVisibleRows] = useState(50);
  const [isLoading, setIsLoading] = useState(false);
  const ROWS_PER_LOAD = 50;
  const LARGE_DATASET_THRESHOLD = 1000;

  // Memoized data processing
  const uniqueCustomers = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    return [
      ...new Set(
        data
          .map((d) => d[chartConfig.xAxis])
          .map((val) => (val === "" ? "Empty String" : val))
          .filter((val) => val != null)
      ),
    ];
  }, [data, chartConfig.xAxis]);

  const uniqueCustomerBinds = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    return [
      ...new Set(
        data
          .map((d) => d[chartConfig.yAxis])
          .map((val) => (val === "" ? "Empty String" : val))
          .filter((val) => val != null)
      ),
    ];
  }, [data, chartConfig.yAxis]);

  const isLargeDataset = uniqueCustomers.length >= LARGE_DATASET_THRESHOLD;

  const headerCellWidth = useMemo(
    () =>
      uniqueCustomerBinds.length >= 2 && uniqueCustomerBinds.length <= 3
        ? `${(100 - 20) / uniqueCustomerBinds.length}%`
        : 150,
    [uniqueCustomerBinds.length]
  );

  const { minValue = 0, maxValue = 0 } = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return { minValue: 0, maxValue: 0 };
    }

    const values = data
      .map((d) => {
        const val = d[chartConfig.derivedFields];
        return val == null ? 0 : Number(val);
      })
      .filter((v) => !isNaN(v));

    const totalDataPoints = uniqueCustomers.length * uniqueCustomerBinds.length;
    const existingDataPoints = data.length;
    const missingDataPoints = totalDataPoints - existingDataPoints;

    for (let i = 0; i < missingDataPoints; i++) {
      values.push(0);
    }

    // Use reduce to avoid stack overflow with large arrays
    const minValue =
      values.length > 0
        ? values.reduce((min, val) => Math.min(min, val), values[0])
        : 0;
    const maxValue =
      values.length > 0
        ? values.reduce((max, val) => Math.max(max, val), values[0])
        : 0;

    return { allValues: values, minValue, maxValue };
  }, [
    data,
    chartConfig.derivedFields,
    uniqueCustomers.length,
    uniqueCustomerBinds.length,
  ]);

  const matrixData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    return uniqueCustomers.map((customer) => {
      const row = { customer };
      uniqueCustomerBinds.forEach((bind) => {
        const dataPoint = data.find((d) => {
          const xValue =
            d[chartConfig.xAxis] === "" ? "Empty String" : d[chartConfig.xAxis];
          const yValue =
            d[chartConfig.yAxis] === "" ? "Empty String" : d[chartConfig.yAxis];
          return xValue === customer && yValue === bind;
        });
        row[bind] = dataPoint
          ? dataPoint[chartConfig.derivedFields] == null
            ? 0
            : Number(dataPoint[chartConfig.derivedFields])
          : 0;
      });
      return row;
    });
  }, [
    uniqueCustomers,
    uniqueCustomerBinds,
    data,
    chartConfig.xAxis,
    chartConfig.yAxis,
    chartConfig.derivedFields,
  ]);

  const visibleMatrixData = useMemo(() => {
    if (!isLargeDataset) return matrixData;
    return matrixData.slice(0, visibleRows);
  }, [matrixData, isLargeDataset, visibleRows]);

  const handleScroll = useCallback(
    (event) => {
      if (!isLargeDataset || isLoading) return;

      const { target } = event;
      const { scrollTop, scrollHeight, clientHeight } = target;

      if (scrollTop + clientHeight >= scrollHeight - 5) {
        if (visibleRows < matrixData.length) {
          setIsLoading(true);
          setTimeout(() => {
            setVisibleRows((prev) =>
              Math.min(prev + ROWS_PER_LOAD, matrixData.length)
            );
            setIsLoading(false);
          }, 300);
        }
      }
    },
    [isLargeDataset, isLoading, visibleRows, matrixData.length, ROWS_PER_LOAD]
  );

  useEffect(() => {
    setVisibleRows(50);
  }, [data]);

  // Early return if no data
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Paper sx={{ p: 4, width: "100%" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: 256,
            borderRadius: 2,
          }}
        >
          {NO_DATA_MESSAGE}
        </div>
      </Paper>
    );
  }

  // Color intensity function
  const getColor = (value) => {
    // Robust hex parsing with fallbacks
    const rawHex = (chartConfig.mapColor || "#00FF00").replace("#", "");
    const hex = rawHex.padEnd(6, "0");
    const r = parseInt(hex.substring(0, 2) || "00", 16);
    const g = parseInt(hex.substring(2, 4) || "FF", 16);
    const b = parseInt(hex.substring(4, 6) || "00", 16);

    let numValue = value == null ? 0 : Number(value);
    if (Number.isNaN(numValue)) numValue = 0;

    // If all values are the same (including single data point), use medium intensity
    if (minValue === maxValue) {
      if (numValue === 0) {
        return `rgba(${r}, ${g}, ${b}, 0.1)`; // Very light for zero
      }
      return `rgba(${r}, ${g}, ${b}, 0.6)`; // Medium intensity for single non-zero value
    }

    // Normalize value to 0..1 across the actual min..max range. This correctly
    // handles negative min values so that min maps to fully transparent (white)
    // and max maps to fully opaque color.
    const intensity = (numValue - minValue) / (maxValue - minValue);
    const clampedIntensity = Math.max(0, Math.min(1, intensity));

    // Map intensity directly to alpha: 0 => transparent (white), 1 => opaque
    const alphaValue = clampedIntensity;

    return `rgba(${r}, ${g}, ${b}, ${alphaValue})`;
  };

  const getTextColor = (value) => {
    let numValue = value == null ? 0 : Number(value);
    if (Number.isNaN(numValue)) numValue = 0;

    // If all values are the same (single data point case)
    if (minValue === maxValue) {
      if (numValue === 0) {
        return "#000000"; // Dark text for light zero background
      }
      return "#FFFFFF"; // White text for medium intensity single value background
    }

    // Compute the same normalized intensity used for background alpha
    const intensity = (numValue - minValue) / (maxValue - minValue);
    const clampedIntensity = Math.max(0, Math.min(1, intensity));
    const alphaValue = 0.2 + clampedIntensity * 0.8;

    // Use white text only when the background is sufficiently dark/opaque
    return alphaValue > 0.6 ? "#FFFFFF" : "#000000";
  };

  const shouldShowTooltip = (text, maxLength = 20) => {
    // Don't show tooltip if we have 2 or fewer data points (columns or rows)
    if (uniqueCustomerBinds.length <= 2 || uniqueCustomers.length <= 2) {
      return false;
    }
    return text && text.length > maxLength;
  };

  const truncateText = (text, maxLength = 20) => {
    // Don't truncate if we have 2 or fewer data points (columns or rows)
    if (uniqueCustomerBinds.length <= 2 || uniqueCustomers.length <= 2) {
      return text;
    }
    if (!text || text.length <= maxLength) return text;
    return text.substring(0, maxLength - 3) + "...";
  };

  return (
    <Paper
      sx={{
        p: 3,
        width: "100%",
        position: "relative",
        overflow: "hidden",
        border: "none",
        boxShadow: "none",
      }}
    >
      {/* Color Range Legend */}
      <Box sx={{ mb: 3, display: "flex", alignItems: "center", gap: 2 }}>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ minWidth: 40 }}
        >
          Range:
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <ColorRangeBar
            gradient={`linear-gradient(to right,
              ${(() => {
                const rawHex = (chartConfig.mapColor || "#00FF00").replace(
                  "#",
                  ""
                );
                const hex = rawHex.padEnd(6, "0");
                const r = parseInt(hex.substring(0, 2) || "00", 16);
                const g = parseInt(hex.substring(2, 4) || "FF", 16);
                const b = parseInt(hex.substring(4, 6) || "00", 16);
                // Start at transparent for the min value -> white background
                return `rgba(${r}, ${g}, ${b}, 0.0), rgba(${r}, ${g}, ${b}, 0.25), rgba(${r}, ${g}, ${b}, 0.6), rgba(${r}, ${g}, ${b}, 1.0)`;
              })()})`}
          />
        </Box>
      </Box>

      {/* Heatmap Table */}
      <StyledTableContainer
        component={Paper}
        variant="outlined"
        onScroll={handleScroll}
        sx={{
          maxHeight: isLargeDataset ? 600 : 800, // Lower height for large datasets
        }}
      >
        <Table size="small" stickyHeader>
          <TableHead>
            <TableRow>
              <CornerCell />
              {uniqueCustomerBinds.map((bind) => (
                <StyledHeaderCell
                  key={bind}
                  align="center"
                  sx={{
                    width: headerCellWidth,
                    minWidth:
                      typeof headerCellWidth === "string"
                        ? "auto"
                        : headerCellWidth,
                  }}
                >
                  {bind.length > 15 &&
                  uniqueCustomerBinds.length > 2 &&
                  uniqueCustomers.length > 2 ? (
                    <Tooltip title={bind} arrow>
                      <Box
                        sx={{
                          height: 48,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        {bind.substring(0, 12) + "..."}
                      </Box>
                    </Tooltip>
                  ) : (
                    <Box
                      sx={{
                        height: 48,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      {bind}
                    </Box>
                  )}
                </StyledHeaderCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleMatrixData.map((row, rowIndex) => (
              <TableRow key={rowIndex} hover>
                <StickyCell>
                  {shouldShowTooltip(row.customer) ? (
                    <Tooltip title={row.customer} arrow>
                      <Typography
                        variant="body2"
                        sx={{
                          overflow: "hidden",
                          textOverflow: "ellipsis",
                          whiteSpace: "nowrap",
                        }}
                      >
                        {truncateText(row.customer)}
                      </Typography>
                    </Tooltip>
                  ) : (
                    <Typography
                      variant="body2"
                      sx={{
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {row.customer}
                    </Typography>
                  )}
                </StickyCell>
                {uniqueCustomerBinds.map((bind) => (
                  <HeatmapCell
                    key={bind}
                    backgroundColor={getColor(row[bind])}
                    sx={{
                      width: headerCellWidth,
                      minWidth:
                        typeof headerCellWidth === "string"
                          ? "auto"
                          : headerCellWidth,
                    }}
                  >
                    <Box
                      sx={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        height: "100%",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontWeight: 500,
                          color: getTextColor(row[bind]),
                        }}
                      >
                        {row[bind] !== null && row[bind] !== undefined
                          ? row[bind]
                          : 0}
                      </Typography>
                    </Box>
                  </HeatmapCell>
                ))}
              </TableRow>
            ))}
            {/* Loading indicator for lazy loading */}
            {isLoading && (
              <TableRow>
                <TableCell
                  colSpan={uniqueCustomerBinds.length + 1}
                  align="center"
                  sx={{ py: 2 }}
                >
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: 1,
                    }}
                  >
                    <CircularProgress size={20} />
                    <Typography variant="body2" color="text.secondary">
                      Loading more rows...
                    </Typography>
                  </Box>
                </TableCell>
              </TableRow>
            )}
            {/* Show row count info for large datasets */}
            {isLargeDataset && (
              <TableRow>
                <TableCell
                  colSpan={uniqueCustomerBinds.length + 1}
                  align="center"
                  sx={{ py: 1, bgcolor: "grey.50" }}
                >
                  <Typography variant="caption" color="text.secondary">
                    Showing {visibleMatrixData.length} of {matrixData.length}{" "}
                    rows
                    {visibleMatrixData.length < matrixData.length &&
                      " - Scroll down to load more"}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </StyledTableContainer>
    </Paper>
  );
}

export default HeatMapChartGraph;
