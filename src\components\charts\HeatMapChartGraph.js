import React, { useMemo } from "react";
import { Box, Typography, Paper, Tooltip } from "@mui/material";
import { styled } from "@mui/material/styles";
import { FixedSizeList as List } from "react-window";
import { NO_DATA_MESSAGE } from "../../common/constants";

const ColorRangeBar = styled(Box)(({ gradient }) => ({
  width: 300,
  height: 20,
  background: gradient,
  borderRadius: 4,
  border: "1px solid #e0e0e0",
}));

// Virtualized row component that maintains original table structure
const VirtualizedRow = React.memo(({ index, style, data }) => {
  const {
    row,
    uniqueCustomerBinds,
    headerCellWidth,
    getColor,
    getTextColor,
    shouldShowTooltip,
    truncateText,
  } = data;
  const rowData = row[index];

  return (
    <div
      style={{
        ...style,
        display: "table",
        width: "100%",
        tableLayout: "fixed",
      }}
    >
      <div style={{ display: "table-row" }}>
        <div
          style={{
            display: "table-cell",
            backgroundColor: "#E7F1FD",
            fontWeight: 500,
            fontSize: "0.75rem",
            position: "sticky",
            left: 0,
            zIndex: 999,
            minWidth: 150,
            maxWidth: 130,
            width: 150,
            border: "1px solid #e0e0e0",
            padding: "12px",
            verticalAlign: "middle",
          }}
        >
          {shouldShowTooltip(rowData.customer) ? (
            <Tooltip title={rowData.customer} arrow>
              <Typography
                variant="body2"
                sx={{
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {truncateText(rowData.customer)}
              </Typography>
            </Tooltip>
          ) : (
            <Typography
              variant="body2"
              sx={{
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
              }}
            >
              {rowData.customer}
            </Typography>
          )}
        </div>
        {uniqueCustomerBinds.map((bind) => (
          <div
            key={bind}
            style={{
              display: "table-cell",
              backgroundColor: getColor(rowData[bind]),
              textAlign: "center",
              fontSize: "0.75rem",
              fontWeight: 500,
              minWidth:
                typeof headerCellWidth === "string" ? "auto" : headerCellWidth,
              width: headerCellWidth,
              height: 48,
              border: "1px solid #e0e0e0",
              padding: "12px",
              verticalAlign: "middle",
              position: "relative",
              zIndex: 1,
            }}
            onMouseEnter={(e) => {
              e.target.style.transform = "scale(1.02)";
              e.target.style.transition = "transform 0.2s ease-in-out";
              e.target.style.zIndex = 50;
              e.target.style.boxShadow = "0 2px 8px rgba(0,0,0,0.15)";
            }}
            onMouseLeave={(e) => {
              e.target.style.transform = "scale(1)";
              e.target.style.zIndex = 1;
              e.target.style.boxShadow = "none";
            }}
          >
            <Typography
              variant="body2"
              sx={{
                fontWeight: 500,
                color: getTextColor(rowData[bind]),
              }}
            >
              {rowData[bind] !== null && rowData[bind] !== undefined
                ? rowData[bind]
                : 0}
            </Typography>
          </div>
        ))}
      </div>
    </div>
  );
});

function HeatMapChartGraph({ data = [], config }) {
  // Default config
  const defaultConfig = {
    xAxis: "Customer",
    yAxis: "Customer Bind",
    derivedFields: "Count",
    mapColor: "#00FF00",
  };

  const chartConfig = { ...defaultConfig, ...config };

  // Virtualization constants
  const ROW_HEIGHT = 48;
  const MAX_VISIBLE_HEIGHT = 600;

  // Memoized data processing
  const uniqueCustomers = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    return [
      ...new Set(
        data
          .map((d) => d[chartConfig.xAxis])
          .map((val) => (val === "" ? "Empty String" : val))
          .filter((val) => val != null)
      ),
    ];
  }, [data, chartConfig.xAxis]);

  const uniqueCustomerBinds = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];
    return [
      ...new Set(
        data
          .map((d) => d[chartConfig.yAxis])
          .map((val) => (val === "" ? "Empty String" : val))
          .filter((val) => val != null)
      ),
    ];
  }, [data, chartConfig.yAxis]);

  const headerCellWidth = useMemo(
    () =>
      uniqueCustomerBinds.length >= 2 && uniqueCustomerBinds.length <= 3
        ? `${(100 - 20) / uniqueCustomerBinds.length}%`
        : 150,
    [uniqueCustomerBinds.length]
  );

  const { minValue = 0, maxValue = 0 } = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return { minValue: 0, maxValue: 0 };
    }

    const values = data
      .map((d) => {
        const val = d[chartConfig.derivedFields];
        return val == null ? 0 : Number(val);
      })
      .filter((v) => !isNaN(v));

    const totalDataPoints = uniqueCustomers.length * uniqueCustomerBinds.length;
    const existingDataPoints = data.length;
    const missingDataPoints = totalDataPoints - existingDataPoints;

    for (let i = 0; i < missingDataPoints; i++) {
      values.push(0);
    }

    // Use reduce to avoid stack overflow with large arrays
    const minValue =
      values.length > 0
        ? values.reduce((min, val) => Math.min(min, val), values[0])
        : 0;
    const maxValue =
      values.length > 0
        ? values.reduce((max, val) => Math.max(max, val), values[0])
        : 0;

    return { allValues: values, minValue, maxValue };
  }, [
    data,
    chartConfig.derivedFields,
    uniqueCustomers.length,
    uniqueCustomerBinds.length,
  ]);

  const matrixData = useMemo(() => {
    if (!data || !Array.isArray(data) || data.length === 0) return [];

    return uniqueCustomers.map((customer) => {
      const row = { customer };
      uniqueCustomerBinds.forEach((bind) => {
        const dataPoint = data.find((d) => {
          const xValue =
            d[chartConfig.xAxis] === "" ? "Empty String" : d[chartConfig.xAxis];
          const yValue =
            d[chartConfig.yAxis] === "" ? "Empty String" : d[chartConfig.yAxis];
          return xValue === customer && yValue === bind;
        });
        row[bind] = dataPoint
          ? dataPoint[chartConfig.derivedFields] == null
            ? 0
            : Number(dataPoint[chartConfig.derivedFields])
          : 0;
      });
      return row;
    });
  }, [
    uniqueCustomers,
    uniqueCustomerBinds,
    data,
    chartConfig.xAxis,
    chartConfig.yAxis,
    chartConfig.derivedFields,
  ]);

  // Helper functions
  const getColor = useMemo(
    () => (value) => {
      // Robust hex parsing with fallbacks
      const rawHex = (chartConfig.mapColor || "#00FF00").replace("#", "");
      const hex = rawHex.padEnd(6, "0");
      const r = parseInt(hex.substring(0, 2) || "00", 16);
      const g = parseInt(hex.substring(2, 4) || "FF", 16);
      const b = parseInt(hex.substring(4, 6) || "00", 16);

      let numValue = value == null ? 0 : Number(value);
      if (Number.isNaN(numValue)) numValue = 0;

      // If all values are the same (including single data point), use medium intensity
      if (minValue === maxValue) {
        if (numValue === 0) {
          return `rgba(${r}, ${g}, ${b}, 0.1)`; // Very light for zero
        }
        return `rgba(${r}, ${g}, ${b}, 0.6)`; // Medium intensity for single non-zero value
      }

      // Normalize value to 0..1 across the actual min..max range
      const intensity = (numValue - minValue) / (maxValue - minValue);
      const clampedIntensity = Math.max(0, Math.min(1, intensity));

      // Map intensity directly to alpha: 0 => transparent (white), 1 => opaque
      const alphaValue = clampedIntensity;

      return `rgba(${r}, ${g}, ${b}, ${alphaValue})`;
    },
    [chartConfig.mapColor, minValue, maxValue]
  );

  const getTextColor = useMemo(
    () => (value) => {
      let numValue = value == null ? 0 : Number(value);
      if (Number.isNaN(numValue)) numValue = 0;

      // If all values are the same (single data point case)
      if (minValue === maxValue) {
        if (numValue === 0) {
          return "#000000"; // Dark text for light zero background
        }
        return "#FFFFFF"; // White text for medium intensity single value background
      }

      // Compute the same normalized intensity used for background alpha
      const intensity = (numValue - minValue) / (maxValue - minValue);
      const clampedIntensity = Math.max(0, Math.min(1, intensity));
      const alphaValue = 0.2 + clampedIntensity * 0.8;

      // Use white text only when the background is sufficiently dark/opaque
      return alphaValue > 0.6 ? "#FFFFFF" : "#000000";
    },
    [minValue, maxValue]
  );

  const shouldShowTooltip = useMemo(
    () =>
      (text, maxLength = 20) => {
        // Don't show tooltip if we have 2 or fewer data points (columns or rows)
        if (uniqueCustomerBinds.length <= 2 || uniqueCustomers.length <= 2) {
          return false;
        }
        return text && text.length > maxLength;
      },
    [uniqueCustomerBinds.length, uniqueCustomers.length]
  );

  const truncateText = useMemo(
    () =>
      (text, maxLength = 20) => {
        // Don't truncate if we have 2 or fewer data points (columns or rows)
        if (uniqueCustomerBinds.length <= 2 || uniqueCustomers.length <= 2) {
          return text;
        }
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + "...";
      },
    [uniqueCustomerBinds.length, uniqueCustomers.length]
  );

  // Calculate the height for the virtualized list
  const listHeight = useMemo(() => {
    const totalRows = matrixData.length;
    const maxHeight = MAX_VISIBLE_HEIGHT;
    const calculatedHeight = totalRows * ROW_HEIGHT;
    return Math.min(calculatedHeight, maxHeight);
  }, [matrixData.length]);

  // Data for the virtualized rows
  const virtualizedData = useMemo(
    () => ({
      row: matrixData,
      uniqueCustomerBinds,
      headerCellWidth,
      getColor,
      getTextColor,
      shouldShowTooltip,
      truncateText,
    }),
    [
      matrixData,
      uniqueCustomerBinds,
      headerCellWidth,
      getColor,
      getTextColor,
      shouldShowTooltip,
      truncateText,
    ]
  );

  // Early return if no data
  if (!data || !Array.isArray(data) || data.length === 0) {
    return (
      <Paper sx={{ p: 4, width: "100%" }}>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            height: 256,
            borderRadius: 2,
          }}
        >
          {NO_DATA_MESSAGE}
        </div>
      </Paper>
    );
  }

  return (
    <Paper
      sx={{
        p: 3,
        width: "100%",
        position: "relative",
        overflow: "hidden",
        border: "none",
        boxShadow: "none",
      }}
    >
      {/* Color Range Legend */}
      <Box sx={{ mb: 3, display: "flex", alignItems: "center", gap: 2 }}>
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{ minWidth: 40 }}
        >
          Range:
        </Typography>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <ColorRangeBar
            gradient={`linear-gradient(to right,
              ${(() => {
                const rawHex = (chartConfig.mapColor || "#00FF00").replace(
                  "#",
                  ""
                );
                const hex = rawHex.padEnd(6, "0");
                const r = parseInt(hex.substring(0, 2) || "00", 16);
                const g = parseInt(hex.substring(2, 4) || "FF", 16);
                const b = parseInt(hex.substring(4, 6) || "00", 16);
                // Start at transparent for the min value -> white background
                return `rgba(${r}, ${g}, ${b}, 0.0), rgba(${r}, ${g}, ${b}, 0.25), rgba(${r}, ${g}, ${b}, 0.6), rgba(${r}, ${g}, ${b}, 1.0)`;
              })()})`}
          />
        </Box>
      </Box>

      {/* Heatmap Table */}
      <Box
        sx={{
          border: "1px solid",
          borderColor: "divider",
          borderRadius: 1,
          maxHeight: 600,
          overflow: "auto",
          position: "relative",
        }}
      >
        {/* Header (Scrollable) */}
        <div style={{ display: "table", width: "100%", tableLayout: "fixed" }}>
          <div style={{ display: "table-row" }}>
            <div
              style={{
                display: "table-cell",
                backgroundColor: "#E7F1FD",
                fontWeight: 600,
                fontSize: "0.75rem",
                position: "sticky",
                left: 0,
                zIndex: 1001,
                minWidth: 150,
                maxWidth: 130,
                width: 150,
                border: "1px solid #e0e0e0",
                padding: "12px",
                verticalAlign: "middle",
                height: "48px",
              }}
            />
            {uniqueCustomerBinds.map((bind) => (
              <div
                key={bind}
                style={{
                  display: "table-cell",
                  backgroundColor: "#E7F1FD",
                  fontWeight: 600,
                  fontSize: "0.75rem",
                  minWidth:
                    typeof headerCellWidth === "string"
                      ? "auto"
                      : headerCellWidth,
                  width: headerCellWidth,
                  border: "1px solid #e0e0e0",
                  padding: "12px",
                  textAlign: "center",
                  verticalAlign: "middle",
                  height: "48px",
                }}
              >
                {bind.length > 15 &&
                uniqueCustomerBinds.length > 2 &&
                uniqueCustomers.length > 2 ? (
                  <Tooltip title={bind} arrow>
                    <Box
                      sx={{
                        height: 24,
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      {bind.substring(0, 12) + "..."}
                    </Box>
                  </Tooltip>
                ) : (
                  <Box
                    sx={{
                      height: 24,
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    {bind}
                  </Box>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Virtualized Body */}
        <div>
          <List
            height={listHeight}
            itemCount={matrixData.length}
            itemSize={ROW_HEIGHT}
            itemData={virtualizedData}
            width="100%"
          >
            {VirtualizedRow}
          </List>
        </div>
      </Box>
    </Paper>
  );
}

export default HeatMapChartGraph;
