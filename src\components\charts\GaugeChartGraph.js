import React, { useState, useMemo, useEffect, useCallback } from "react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts";
import { Formik, Form } from "formik";
import Select from "../FormsUI/Select";

const COLORS = ["#83B776", "#DC9033"]; // Green for actual, Orange for expected remainder

const GaugeChartGraph = ({ data = [], config = {} }) => {
  const [chartData, setChartData] = useState(null);
  const [selectedDate, setSelectedDate] = useState(null); // Initialize as null instead of empty string

  const {
    xAxis: selectorField,
    yAxis: yAxisFields,
    configExpectedValue: expectedValue,
  } = config;

  const dateOptions = useMemo(
    () =>
      selectorField && data.length > 0
        ? data.map((d) => {
            // Handle null, empty string, and undefined in selectorField (xAxis)
            const fieldValue =
              d[selectorField] === null ||
              d[selectorField] === "" ||
              d[selectorField] === undefined
                ? "Empty String"
                : d[selectorField];
            return {
              value: fieldValue,
              label: fieldValue === 0 ? "0" : fieldValue, // Ensure 0 is displayed as "0"
            };
          })
        : [],
    [data, selectorField]
  );

  const handleSubmit = useCallback(
    (values) => {
      if (
        values.selectedDate === undefined ||
        values.selectedDate === null ||
        !yAxisFields
      )
        return;

      // Handle the case where selectedDate is "Empty String"
      const searchValue =
        values.selectedDate === "Empty String" ? "" : values.selectedDate;

      const selectedData = data.find((d) => {
        const fieldValue = d[selectorField];
        // Check for null, empty string, or undefined values
        if (values.selectedDate === "Empty String") {
          return (
            fieldValue === null || fieldValue === "" || fieldValue === undefined
          );
        }
        return fieldValue === searchValue;
      });

      if (selectedData) {
        // Ensure numeric values and handle edge cases (null/undefined/strings)
        const actualValue = Number(selectedData[yAxisFields]) || 0;
        const ev = Number(expectedValue) || 0;

        // Raw percentage (may be > 100)
        const percentage =
          ev > 0 ? (actualValue / ev) * 100 : actualValue > 0 ? 100 : 0;
        // Display percentage is capped at 100 so the gauge visually fully occupies when actual >> expected
        const displayPercentage = Math.min(percentage, 100);

        // For the pie chart slices: cap the Actual slice to at most the expected value so it can fill the chart.
        // If expected is 0 and actual > 0, show a full fill by using a non-zero slice for Actual and zero for Expected.
        const actualSlice =
          ev > 0 ? Math.min(actualValue, ev) : actualValue > 0 ? 1 : 0;
        const expectedSlice = ev > 0 ? Math.max(ev - actualSlice, 0) : 0;

        setChartData({
          gaugeData: [
            { name: "Actual", value: actualSlice },
            { name: "Expected", value: expectedSlice },
          ],
          actualValue,
          expectedValue: ev,
          percentage,
          displayPercentage,
        });
      }
    },
    [data, selectorField, yAxisFields, expectedValue]
  );

  useEffect(() => {
    if (dateOptions.length > 0) {
      const firstOption = dateOptions[0].value;
      setSelectedDate(firstOption);
      handleSubmit({ selectedDate: firstOption });
    }
  }, [dateOptions, handleSubmit]);

  // Reset to first option when component mounts or data/config changes
  useEffect(() => {
    if (dateOptions.length > 0) {
      const firstOptionValue = dateOptions[0].value;
      setSelectedDate(firstOptionValue);
    }
  }, [data, config, dateOptions]);

  if (!data || data.length === 0)
    return <div className="text-center text-gray-500">No data available</div>;
  if (!selectorField || !yAxisFields)
    return (
      <div className="text-center text-red-500">
        Chart configuration is incomplete.
      </div>
    );

  return (
    <>
      <div className="w-full max-w-md ">
        <Formik
          initialValues={{
            selectedDate: selectedDate !== null ? selectedDate : "",
          }}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue }) => (
            <Form className="flex items-end gap-4 mb-4 w-10/12">
              <div className="flex-grow mx-4 ">
                <label
                  htmlFor="selectedDate"
                  className="block text-sm font-medium text-gray-700 text-left mb-2"
                >
                  Select a {selectorField}
                </label>
                <Select
                  id="selectedDate"
                  name="selectedDate"
                  options={dateOptions}
                  placeholder={`Select a ${selectorField}`}
                  value={values.selectedDate}
                  onChange={(selectedOption) => {
                    const newValue = selectedOption.value;
                    setSelectedDate(newValue);
                    setFieldValue("selectedDate", newValue);
                    handleSubmit({ selectedDate: newValue });
                  }}
                />
              </div>
              {/* <Button label="Apply" type="submit" /> */}
            </Form>
          )}
        </Formik>
      </div>
      {chartData && (
        <div className="flex flex-col items-center">
          <ResponsiveContainer width="100%" height={300}>
            <PieChart margin={{ top: 40 }}>
              <Pie
                data={chartData.gaugeData}
                startAngle={180}
                endAngle={0}
                innerRadius={"85%"}
                outerRadius={"130%"}
                dataKey="value"
              >
                {chartData.gaugeData.map((entry, index) => (
                  <Cell
                    key={`cell-${index}`}
                    fill={COLORS[index % COLORS.length]}
                  />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>

          {/* Percentage + values in center */}
          <div className="text-center -mt-48">
            <p className="text-xl text-black mb-3">
              {(() => {
                const p = Number(chartData.percentage) || 0;
                const fixed = p.toFixed(2);
                // If decimals are .00, show integer without decimals, else show two decimals
                if (fixed.endsWith(".00")) return `${Math.round(p)}%`;
                return `${fixed}%`;
              })()}
            </p>
            <p>
              <span className="text-[#83B776] ">{chartData.actualValue}</span>
              <span className="text-black"> / </span>
              <span className="text-[#DC9033] ">{chartData.expectedValue}</span>
            </p>
          </div>
        </div>
      )}
      {/* Legend */}
      <div className="flex flex-col gap-3 mt-4 mx-8">
        <div className="flex items-center  gap-2">
          <span
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: COLORS[0] }}
          ></span>
          <span className="text-sm text-[#83B776]">
            {config.xAxis === "" ? "Empty String" : config.xAxis}
          </span>
        </div>
        <div className="flex items-center  gap-2">
          <span
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: COLORS[1] }}
          ></span>
          <span className="text-sm text-[#DC9033]">Expected</span>
        </div>
      </div>
    </>
  );
};

export default GaugeChartGraph;
